const { supabase } = require("../config/supabase")
const { broadcastJobToProviders } = require("../services/jobBroadcast")
const { createNotification } = require("../services/notifications")

// Create a new booking (customers only)
const createBooking = async (req, res) => {
  try {
    const { service_id, scheduled_date, scheduled_time, location, service_details, duration_minutes } = req.body

    // Validate required fields
    if (!service_id || !scheduled_date || !scheduled_time || !location) {
      return res.status(400).json({
        error: "Missing required fields: service_id, scheduled_date, scheduled_time, location",
      })
    }

    // Verify service exists
    const { data: service, error: serviceError } = await supabase
      .from("services")
      .select("*")
      .eq("id", service_id)
      .eq("is_active", true)
      .single()

    if (serviceError || !service) {
      return res.status(404).json({ error: "Service not found or inactive" })
    }

    // Create booking
    const bookingData = {
      customer_id: req.user.id,
      service_id,
      scheduled_date,
      scheduled_time,
      location,
      service_details: service_details || {
        base_price: service.base_price,
        add_ons: [],
        total_price: service.base_price,
      },
      duration_minutes: duration_minutes || service.duration_minutes,
      status: "pending",
      expires_at: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
    }

    const { data: booking, error: bookingError } = await supabase
      .from("bookings")
      .insert(bookingData)
      .select("*")
      .single()

    if (bookingError) {
      console.error("Booking creation error:", bookingError)
      return res.status(500).json({ error: "Failed to create booking" })
    }

    // Broadcast job to eligible providers
    try {
      await broadcastJobToProviders(booking)
    } catch (broadcastError) {
      console.error("Job broadcast error:", broadcastError)
      // Don't fail the booking creation if broadcast fails
    }

    res.status(201).json({
      message: "Booking created successfully",
      booking,
    })
  } catch (error) {
    console.error("Create booking error:", error)
    res.status(500).json({ error: "Failed to create booking" })
  }
}

// Get user's bookings
const getUserBookings = async (req, res) => {
  try {
    const { status, limit = 20, offset = 0 } = req.query

    let query = supabase
      .from("bookings")
      .select(`
        *,
        customer:users!bookings_customer_id_fkey(id, full_name, phone, avatar_url),
        provider:users!bookings_provider_id_fkey(id, full_name, phone, avatar_url, rating),
        service:services(id, title, category, base_price)
      `)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    // Filter based on user role
    if (req.user.role === "customer") {
      query = query.eq("customer_id", req.user.id)
    } else if (req.user.role === "provider") {
      query = query.eq("provider_id", req.user.id)
    }
    // Admins can see all bookings

    if (status) {
      query = query.eq("status", status)
    }

    const { data: bookings, error } = await query

    if (error) {
      console.error("Get bookings error:", error)
      return res.status(500).json({ error: "Failed to fetch bookings" })
    }

    res.json({ bookings })
  } catch (error) {
    console.error("Get bookings error:", error)
    res.status(500).json({ error: "Failed to fetch bookings" })
  }
}

// Get specific booking
const getBookingById = async (req, res) => {
  try {
    const { id } = req.params

    const { data: booking, error } = await supabase
      .from("bookings")
      .select(`
        *,
        customer:users!bookings_customer_id_fkey(id, full_name, phone, avatar_url),
        provider:users!bookings_provider_id_fkey(id, full_name, phone, avatar_url, rating),
        service:services(id, title, category, base_price, description)
      `)
      .eq("id", id)
      .single()

    if (error || !booking) {
      return res.status(404).json({ error: "Booking not found" })
    }

    // Check access permissions
    const hasAccess =
      req.user.role === "admin" || booking.customer_id === req.user.id || booking.provider_id === req.user.id

    if (!hasAccess) {
      return res.status(403).json({ error: "Access denied" })
    }

    res.json({ booking })
  } catch (error) {
    console.error("Get booking error:", error)
    res.status(500).json({ error: "Failed to fetch booking" })
  }
}

// Accept a booking (providers only)
const acceptBooking = async (req, res) => {
  try {
    const { id } = req.params

    // Get booking details
    const { data: booking, error: fetchError } = await supabase
      .from("bookings")
      .select("*")
      .eq("id", id)
      .eq("status", "pending")
      .single()

    if (fetchError || !booking) {
      return res.status(404).json({ error: "Booking not found or already accepted" })
    }

    // Check if booking has expired
    if (new Date() > new Date(booking.expires_at)) {
      // Update booking status to expired
      await supabase.from("bookings").update({ status: "expired" }).eq("id", id)

      return res.status(400).json({ error: "Booking has expired" })
    }

    // Check if provider was invited to this job
    const { data: request } = await supabase
      .from("booking_requests")
      .select("*")
      .eq("booking_id", id)
      .eq("provider_id", req.user.id)
      .single()

    if (!request) {
      return res.status(403).json({ error: "You were not invited to this job" })
    }

    // Accept the booking
    const { data: updatedBooking, error: updateError } = await supabase
      .from("bookings")
      .update({
        provider_id: req.user.id,
        status: "accepted",
        accepted_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select("*, customer:users!bookings_customer_id_fkey(id, full_name)")
      .single()

    if (updateError) {
      console.error("Accept booking error:", updateError)
      return res.status(500).json({ error: "Failed to accept booking" })
    }

    // Update booking request
    await supabase
      .from("booking_requests")
      .update({
        response: "accepted",
        responded_at: new Date().toISOString(),
      })
      .eq("booking_id", id)
      .eq("provider_id", req.user.id)

    // Notify customer
    await createNotification({
      user_id: booking.customer_id,
      type: "booking_accepted",
      title: "Booking Accepted",
      message: `Your booking has been accepted by ${req.user.profile.full_name}`,
      data: { booking_id: id },
    })

    res.json({
      message: "Booking accepted successfully",
      booking: updatedBooking,
    })
  } catch (error) {
    console.error("Accept booking error:", error)
    res.status(500).json({ error: "Failed to accept booking" })
  }
}

// Update booking status
const updateBookingStatus = async (req, res) => {
  try {
    const { id } = req.params
    const { status, cancellation_reason } = req.body

    const validStatuses = ["in_progress", "completed", "cancelled"]
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        error: "Invalid status. Must be one of: " + validStatuses.join(", "),
      })
    }

    // Get booking
    const { data: booking, error: fetchError } = await supabase.from("bookings").select("*").eq("id", id).single()

    if (fetchError || !booking) {
      return res.status(404).json({ error: "Booking not found" })
    }

    // Check permissions
    const canUpdate =
      req.user.role === "admin" || booking.customer_id === req.user.id || booking.provider_id === req.user.id

    if (!canUpdate) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Prepare update data
    const updateData = { status }

    if (status === "completed") {
      updateData.completed_at = new Date().toISOString()
    }

    if (status === "cancelled" && cancellation_reason) {
      updateData.cancellation_reason = cancellation_reason
    }

    // Update booking
    const { data: updatedBooking, error: updateError } = await supabase
      .from("bookings")
      .update(updateData)
      .eq("id", id)
      .select("*")
      .single()

    if (updateError) {
      console.error("Update booking status error:", updateError)
      return res.status(500).json({ error: "Failed to update booking status" })
    }

    // Send notifications based on status
    if (status === "completed") {
      // Notify both parties
      await createNotification({
        user_id: booking.customer_id,
        type: "booking_completed",
        title: "Service Completed",
        message: "Your service has been completed. Please leave a review!",
        data: { booking_id: id },
      })

      if (booking.provider_id) {
        await createNotification({
          user_id: booking.provider_id,
          type: "booking_completed",
          title: "Service Completed",
          message: "Service completed successfully!",
          data: { booking_id: id },
        })
      }
    }

    res.json({
      message: "Booking status updated successfully",
      booking: updatedBooking,
    })
  } catch (error) {
    console.error("Update booking status error:", error)
    res.status(500).json({ error: "Failed to update booking status" })
  }
}

module.exports = {
  createBooking,
  getUserBookings,
  getBookingById,
  acceptBooking,
  updateBookingStatus,
}
