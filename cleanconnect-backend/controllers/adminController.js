const { supabase } = require("../config/supabase")

// Admin dashboard stats
const getDashboardStats = async (req, res) => {
  try {
    // Get various statistics
    const [
      { data: totalUsers },
      { data: totalBookings },
      { data: activeProviders },
      { data: recentBookings },
      { data: monthlyRevenue },
    ] = await Promise.all([
      supabase.from("users").select("id", { count: "exact" }),
      supabase.from("bookings").select("id", { count: "exact" }),
      supabase.from("provider_profiles").select("user_id", { count: "exact" }).eq("is_approved", true),
      supabase.from("bookings").select("*").order("created_at", { ascending: false }).limit(10),
      supabase
        .from("bookings")
        .select("service_details, completed_at")
        .eq("status", "completed")
        .gte("completed_at", new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString()),
    ])

    const stats = {
      total_users: totalUsers?.length || 0,
      total_bookings: totalBookings?.length || 0,
      active_providers: activeProviders?.length || 0,
      monthly_revenue:
        monthlyRevenue?.reduce((sum, booking) => sum + (booking.service_details?.total_price || 0), 0) || 0,
      recent_bookings: recentBookings || [],
    }

    res.json({ stats })
  } catch (error) {
    console.error("Admin stats error:", error)
    res.status(500).json({ error: "Failed to fetch admin stats" })
  }
}

// Get all users with filters
const getAllUsers = async (req, res) => {
  try {
    const { role, is_active, limit = 50, offset = 0 } = req.query

    let query = supabase
      .from("users")
      .select("*")
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (role) query = query.eq("role", role)
    if (is_active !== undefined) query = query.eq("is_active", is_active === "true")

    const { data: users, error } = await query

    if (error) {
      console.error("Get users error:", error)
      return res.status(500).json({ error: "Failed to fetch users" })
    }

    res.json({ users })
  } catch (error) {
    console.error("Get users error:", error)
    res.status(500).json({ error: "Failed to fetch users" })
  }
}

// Approve/reject provider
const updateProviderApproval = async (req, res) => {
  try {
    const { id } = req.params
    const { is_approved, rejection_reason } = req.body

    const updateData = {
      is_approved,
      approval_date: is_approved ? new Date().toISOString() : null,
    }

    const { data: updatedProvider, error } = await supabase
      .from("provider_profiles")
      .update(updateData)
      .eq("user_id", id)
      .select("*, user:users(full_name, email)")
      .single()

    if (error) {
      console.error("Update provider approval error:", error)
      return res.status(500).json({ error: "Failed to update provider approval" })
    }

    // Send notification to provider
    const notificationTitle = is_approved ? "Application Approved" : "Application Rejected"
    const notificationMessage = is_approved
      ? "Congratulations! Your provider application has been approved."
      : `Your provider application has been rejected. ${rejection_reason || ""}`

    await supabase.from("notifications").insert({
      user_id: id,
      type: "system_alert",
      title: notificationTitle,
      message: notificationMessage,
      data: { approval_status: is_approved },
    })

    res.json({
      message: `Provider ${is_approved ? "approved" : "rejected"} successfully`,
      provider: updatedProvider,
    })
  } catch (error) {
    console.error("Provider approval error:", error)
    res.status(500).json({ error: "Failed to update provider approval" })
  }
}

// Get all bookings for admin
const getAllBookings = async (req, res) => {
  try {
    const { status, limit = 50, offset = 0 } = req.query

    let query = supabase
      .from("bookings")
      .select(`
        *,
        customer:users!bookings_customer_id_fkey(id, full_name, phone),
        provider:users!bookings_provider_id_fkey(id, full_name, phone),
        service:services(id, title, category)
      `)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (status) query = query.eq("status", status)

    const { data: bookings, error } = await query

    if (error) {
      console.error("Get admin bookings error:", error)
      return res.status(500).json({ error: "Failed to fetch bookings" })
    }

    res.json({ bookings })
  } catch (error) {
    console.error("Get admin bookings error:", error)
    res.status(500).json({ error: "Failed to fetch bookings" })
  }
}

// Flag/unflag user
const updateUserStatus = async (req, res) => {
  try {
    const { id } = req.params
    const { is_active, reason } = req.body

    const { data: updatedUser, error } = await supabase
      .from("users")
      .update({ is_active })
      .eq("id", id)
      .select("*")
      .single()

    if (error) {
      console.error("Flag user error:", error)
      return res.status(500).json({ error: "Failed to update user status" })
    }

    // Send notification to user if flagged
    if (!is_active) {
      await supabase.from("notifications").insert({
        user_id: id,
        type: "system_alert",
        title: "Account Suspended",
        message: `Your account has been suspended. ${reason || "Please contact support for more information."}`,
        data: { suspension_reason: reason },
      })
    }

    res.json({
      message: `User ${is_active ? "activated" : "deactivated"} successfully`,
      user: updatedUser,
    })
  } catch (error) {
    console.error("Flag user error:", error)
    res.status(500).json({ error: "Failed to update user status" })
  }
}

// Get provider applications
const getProviderApplications = async (req, res) => {
  try {
    const { status = "pending", limit = 50, offset = 0 } = req.query

    let query = supabase
      .from("provider_profiles")
      .select(`
        *,
        user:users(id, full_name, email, phone, created_at)
      `)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1)

    if (status === "pending") {
      query = query.eq("is_approved", false)
    } else if (status === "approved") {
      query = query.eq("is_approved", true)
    }

    const { data: applications, error } = await query

    if (error) {
      console.error("Get provider applications error:", error)
      return res.status(500).json({ error: "Failed to fetch applications" })
    }

    res.json({ applications })
  } catch (error) {
    console.error("Get provider applications error:", error)
    res.status(500).json({ error: "Failed to fetch applications" })
  }
}

module.exports = {
  getDashboardStats,
  getAllUsers,
  updateProviderApproval,
  getAllBookings,
  updateUserStatus,
  getProviderApplications,
}
