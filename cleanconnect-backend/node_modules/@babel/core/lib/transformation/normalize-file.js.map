{"version": 3, "names": ["_fs", "data", "require", "_path", "_debug", "_t", "_convertSourceMap", "_file", "_index", "_cloneDeep", "file", "traverseFast", "debug", "buildDebug", "INLINE_SOURCEMAP_REGEX", "EXTERNAL_SOURCEMAP_REGEX", "normalizeFile", "pluginPasses", "options", "code", "ast", "type", "Error", "cloneInputAst", "cloneDeep", "parser", "inputMap", "inputSourceMap", "convertSourceMap", "fromObject", "lastComment", "extractComments", "fromComment", "err", "filename", "match", "exec", "inputMapContent", "fs", "readFileSync", "path", "resolve", "dirname", "fromJSON", "File", "extractCommentsFromList", "regex", "comments", "filter", "value", "test", "node", "leadingComments", "innerComments", "trailingComments"], "sources": ["../../src/transformation/normalize-file.ts"], "sourcesContent": ["import fs from \"node:fs\";\nimport path from \"node:path\";\nimport buildDebug from \"debug\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { file, traverseFast } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { PluginPasses } from \"../config/index.ts\";\nimport convertSourceMap from \"convert-source-map\";\nimport type { SourceMapConverter as Converter } from \"convert-source-map\";\nimport File from \"./file/file.ts\";\nimport parser from \"../parser/index.ts\";\nimport cloneDeep from \"./util/clone-deep.ts\";\n\nconst debug = buildDebug(\"babel:transform:file\");\n\n// These regexps are copied from the convert-source-map package,\n// but without // or /* at the beginning of the comment.\n\nconst INLINE_SOURCEMAP_REGEX =\n  /^[@#]\\s+sourceMappingURL=data:(?:application|text)\\/json;(?:charset[:=]\\S+?;)?base64,.*$/;\nconst EXTERNAL_SOURCEMAP_REGEX =\n  /^[@#][ \\t]+sourceMappingURL=([^\\s'\"`]+)[ \\t]*$/;\n\nexport type NormalizedFile = {\n  code: string;\n  ast: t.File;\n  inputMap: Converter | null;\n};\n\nexport default function* normalizeFile(\n  pluginPasses: PluginPasses,\n  options: { [key: string]: any },\n  code: string,\n  ast?: t.File | t.Program | null,\n): Handler<File> {\n  code = `${code || \"\"}`;\n\n  if (ast) {\n    if (ast.type === \"Program\") {\n      ast = file(ast, [], []);\n    } else if (ast.type !== \"File\") {\n      throw new Error(\"AST root must be a Program or File node\");\n    }\n\n    if (options.cloneInputAst) {\n      ast = cloneDeep(ast);\n    }\n  } else {\n    ast = yield* parser(pluginPasses, options, code);\n  }\n\n  let inputMap = null;\n  if (options.inputSourceMap !== false) {\n    // If an explicit object is passed in, it overrides the processing of\n    // source maps that may be in the file itself.\n    if (typeof options.inputSourceMap === \"object\") {\n      inputMap = convertSourceMap.fromObject(options.inputSourceMap);\n    }\n\n    if (!inputMap) {\n      const lastComment = extractComments(INLINE_SOURCEMAP_REGEX, ast);\n      if (lastComment) {\n        try {\n          inputMap = convertSourceMap.fromComment(\"//\" + lastComment);\n        } catch (err) {\n          if (process.env.BABEL_8_BREAKING) {\n            console.warn(\n              \"discarding unknown inline input sourcemap\",\n              options.filename,\n              err,\n            );\n          } else {\n            debug(\"discarding unknown inline input sourcemap\");\n          }\n        }\n      }\n    }\n\n    if (!inputMap) {\n      const lastComment = extractComments(EXTERNAL_SOURCEMAP_REGEX, ast);\n      if (typeof options.filename === \"string\" && lastComment) {\n        try {\n          // when `lastComment` is non-null, EXTERNAL_SOURCEMAP_REGEX must have matches\n          const match: [string, string] = EXTERNAL_SOURCEMAP_REGEX.exec(\n            lastComment,\n          ) as any;\n          const inputMapContent = fs.readFileSync(\n            path.resolve(path.dirname(options.filename), match[1]),\n            \"utf8\",\n          );\n          inputMap = convertSourceMap.fromJSON(inputMapContent);\n        } catch (err) {\n          debug(\"discarding unknown file input sourcemap\", err);\n        }\n      } else if (lastComment) {\n        debug(\"discarding un-loadable file input sourcemap\");\n      }\n    }\n  }\n\n  return new File(options, {\n    code,\n    ast: ast,\n    inputMap,\n  });\n}\n\nfunction extractCommentsFromList(\n  regex: RegExp,\n  comments: t.Comment[],\n  lastComment: string | null,\n): [t.Comment[], string | null] {\n  if (comments) {\n    comments = comments.filter(({ value }) => {\n      if (regex.test(value)) {\n        lastComment = value;\n        return false;\n      }\n      return true;\n    });\n  }\n  return [comments, lastComment];\n}\n\nfunction extractComments(regex: RegExp, ast: t.Node) {\n  let lastComment: string = null;\n  traverseFast(ast, node => {\n    [node.leadingComments, lastComment] = extractCommentsFromList(\n      regex,\n      node.leadingComments,\n      lastComment,\n    );\n    [node.innerComments, lastComment] = extractCommentsFromList(\n      regex,\n      node.innerComments,\n      lastComment,\n    );\n    [node.trailingComments, lastComment] = extractCommentsFromList(\n      regex,\n      node.trailingComments,\n      lastComment,\n    );\n  });\n  return lastComment;\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,IAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,GAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,GAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,EAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAK,kBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,iBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAM,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAA6C;EAPpCQ,IAAI;EAAEC;AAAY,IAAAN,EAAA;AAS3B,MAAMO,KAAK,GAAGC,OAASA,CAAC,CAAC,sBAAsB,CAAC;AAKhD,MAAMC,sBAAsB,GAC1B,0FAA0F;AAC5F,MAAMC,wBAAwB,GAC5B,gDAAgD;AAQnC,UAAUC,aAAaA,CACpCC,YAA0B,EAC1BC,OAA+B,EAC/BC,IAAY,EACZC,GAA+B,EAChB;EACfD,IAAI,GAAG,GAAGA,IAAI,IAAI,EAAE,EAAE;EAEtB,IAAIC,GAAG,EAAE;IACP,IAAIA,GAAG,CAACC,IAAI,KAAK,SAAS,EAAE;MAC1BD,GAAG,GAAGV,IAAI,CAACU,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;IACzB,CAAC,MAAM,IAAIA,GAAG,CAACC,IAAI,KAAK,MAAM,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IAEA,IAAIJ,OAAO,CAACK,aAAa,EAAE;MACzBH,GAAG,GAAG,IAAAI,kBAAS,EAACJ,GAAG,CAAC;IACtB;EACF,CAAC,MAAM;IACLA,GAAG,GAAG,OAAO,IAAAK,cAAM,EAACR,YAAY,EAAEC,OAAO,EAAEC,IAAI,CAAC;EAClD;EAEA,IAAIO,QAAQ,GAAG,IAAI;EACnB,IAAIR,OAAO,CAACS,cAAc,KAAK,KAAK,EAAE;IAGpC,IAAI,OAAOT,OAAO,CAACS,cAAc,KAAK,QAAQ,EAAE;MAC9CD,QAAQ,GAAGE,kBAAeA,CAAC,CAACC,UAAU,CAACX,OAAO,CAACS,cAAc,CAAC;IAChE;IAEA,IAAI,CAACD,QAAQ,EAAE;MACb,MAAMI,WAAW,GAAGC,eAAe,CAACjB,sBAAsB,EAAEM,GAAG,CAAC;MAChE,IAAIU,WAAW,EAAE;QACf,IAAI;UACFJ,QAAQ,GAAGE,kBAAeA,CAAC,CAACI,WAAW,CAAC,IAAI,GAAGF,WAAW,CAAC;QAC7D,CAAC,CAAC,OAAOG,GAAG,EAAE;UAOL;YACLrB,KAAK,CAAC,2CAA2C,CAAC;UACpD;QACF;MACF;IACF;IAEA,IAAI,CAACc,QAAQ,EAAE;MACb,MAAMI,WAAW,GAAGC,eAAe,CAAChB,wBAAwB,EAAEK,GAAG,CAAC;MAClE,IAAI,OAAOF,OAAO,CAACgB,QAAQ,KAAK,QAAQ,IAAIJ,WAAW,EAAE;QACvD,IAAI;UAEF,MAAMK,KAAuB,GAAGpB,wBAAwB,CAACqB,IAAI,CAC3DN,WACF,CAAQ;UACR,MAAMO,eAAe,GAAGC,IAACA,CAAC,CAACC,YAAY,CACrCC,MAAGA,CAAC,CAACC,OAAO,CAACD,MAAGA,CAAC,CAACE,OAAO,CAACxB,OAAO,CAACgB,QAAQ,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,EACtD,MACF,CAAC;UACDT,QAAQ,GAAGE,kBAAeA,CAAC,CAACe,QAAQ,CAACN,eAAe,CAAC;QACvD,CAAC,CAAC,OAAOJ,GAAG,EAAE;UACZrB,KAAK,CAAC,yCAAyC,EAAEqB,GAAG,CAAC;QACvD;MACF,CAAC,MAAM,IAAIH,WAAW,EAAE;QACtBlB,KAAK,CAAC,6CAA6C,CAAC;MACtD;IACF;EACF;EAEA,OAAO,IAAIgC,aAAI,CAAC1B,OAAO,EAAE;IACvBC,IAAI;IACJC,GAAG,EAAEA,GAAG;IACRM;EACF,CAAC,CAAC;AACJ;AAEA,SAASmB,uBAAuBA,CAC9BC,KAAa,EACbC,QAAqB,EACrBjB,WAA0B,EACI;EAC9B,IAAIiB,QAAQ,EAAE;IACZA,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAC,CAAC;MAAEC;IAAM,CAAC,KAAK;MACxC,IAAIH,KAAK,CAACI,IAAI,CAACD,KAAK,CAAC,EAAE;QACrBnB,WAAW,GAAGmB,KAAK;QACnB,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EACA,OAAO,CAACF,QAAQ,EAAEjB,WAAW,CAAC;AAChC;AAEA,SAASC,eAAeA,CAACe,KAAa,EAAE1B,GAAW,EAAE;EACnD,IAAIU,WAAmB,GAAG,IAAI;EAC9BnB,YAAY,CAACS,GAAG,EAAE+B,IAAI,IAAI;IACxB,CAACA,IAAI,CAACC,eAAe,EAAEtB,WAAW,CAAC,GAAGe,uBAAuB,CAC3DC,KAAK,EACLK,IAAI,CAACC,eAAe,EACpBtB,WACF,CAAC;IACD,CAACqB,IAAI,CAACE,aAAa,EAAEvB,WAAW,CAAC,GAAGe,uBAAuB,CACzDC,KAAK,EACLK,IAAI,CAACE,aAAa,EAClBvB,WACF,CAAC;IACD,CAACqB,IAAI,CAACG,gBAAgB,EAAExB,WAAW,CAAC,GAAGe,uBAAuB,CAC5DC,KAAK,EACLK,IAAI,CAACG,gBAAgB,EACrBxB,WACF,CAAC;EACH,CAAC,CAAC;EACF,OAAOA,WAAW;AACpB;AAAC", "ignoreList": []}