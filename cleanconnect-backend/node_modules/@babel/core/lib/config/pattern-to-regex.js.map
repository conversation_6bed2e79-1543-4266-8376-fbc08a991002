{"version": 3, "names": ["_path", "data", "require", "sep", "path", "endSep", "substitution", "starPat", "starPatLast", "starStarPat", "starStarPatLast", "escapeRegExp", "string", "replace", "pathToPattern", "pattern", "dirname", "parts", "resolve", "split", "RegExp", "map", "part", "i", "last", "length", "indexOf", "slice", "join"], "sources": ["../../src/config/pattern-to-regex.ts"], "sourcesContent": ["import path from \"node:path\";\n\nconst sep = `\\\\${path.sep}`;\nconst endSep = `(?:${sep}|$)`;\n\nconst substitution = `[^${sep}]+`;\n\nconst starPat = `(?:${substitution}${sep})`;\nconst starPatLast = `(?:${substitution}${endSep})`;\n\nconst starStarPat = `${starPat}*?`;\nconst starStarPatLast = `${starPat}*?${starPatLast}?`;\n\nfunction escapeRegExp(string: string) {\n  return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\");\n}\n\n/**\n * Implement basic pattern matching that will allow users to do the simple\n * tests with * and **. If users want full complex pattern matching, then can\n * always use regex matching, or function validation.\n */\nexport default function pathToPattern(\n  pattern: string,\n  dirname: string,\n): RegExp {\n  const parts = path.resolve(dirname, pattern).split(path.sep);\n\n  return new RegExp(\n    [\n      \"^\",\n      ...parts.map((part, i) => {\n        const last = i === parts.length - 1;\n\n        // ** matches 0 or more path parts.\n        if (part === \"**\") return last ? starStarPatLast : starStarPat;\n\n        // * matches 1 path part.\n        if (part === \"*\") return last ? starPatLast : starPat;\n\n        // *.ext matches a wildcard with an extension.\n        if (part.indexOf(\"*.\") === 0) {\n          return (\n            substitution + escapeRegExp(part.slice(1)) + (last ? endSep : sep)\n          );\n        }\n\n        // Otherwise match the pattern text.\n        return escapeRegExp(part) + (last ? endSep : sep);\n      }),\n    ].join(\"\"),\n  );\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAME,GAAG,GAAG,KAAKC,MAAGA,CAAC,CAACD,GAAG,EAAE;AAC3B,MAAME,MAAM,GAAG,MAAMF,GAAG,KAAK;AAE7B,MAAMG,YAAY,GAAG,KAAKH,GAAG,IAAI;AAEjC,MAAMI,OAAO,GAAG,MAAMD,YAAY,GAAGH,GAAG,GAAG;AAC3C,MAAMK,WAAW,GAAG,MAAMF,YAAY,GAAGD,MAAM,GAAG;AAElD,MAAMI,WAAW,GAAG,GAAGF,OAAO,IAAI;AAClC,MAAMG,eAAe,GAAG,GAAGH,OAAO,KAAKC,WAAW,GAAG;AAErD,SAASG,YAAYA,CAACC,MAAc,EAAE;EACpC,OAAOA,MAAM,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;AACtD;AAOe,SAASC,aAAaA,CACnCC,OAAe,EACfC,OAAe,EACP;EACR,MAAMC,KAAK,GAAGb,MAAGA,CAAC,CAACc,OAAO,CAACF,OAAO,EAAED,OAAO,CAAC,CAACI,KAAK,CAACf,MAAGA,CAAC,CAACD,GAAG,CAAC;EAE5D,OAAO,IAAIiB,MAAM,CACf,CACE,GAAG,EACH,GAAGH,KAAK,CAACI,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;IACxB,MAAMC,IAAI,GAAGD,CAAC,KAAKN,KAAK,CAACQ,MAAM,GAAG,CAAC;IAGnC,IAAIH,IAAI,KAAK,IAAI,EAAE,OAAOE,IAAI,GAAGd,eAAe,GAAGD,WAAW;IAG9D,IAAIa,IAAI,KAAK,GAAG,EAAE,OAAOE,IAAI,GAAGhB,WAAW,GAAGD,OAAO;IAGrD,IAAIe,IAAI,CAACI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MAC5B,OACEpB,YAAY,GAAGK,YAAY,CAACW,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIH,IAAI,GAAGnB,MAAM,GAAGF,GAAG,CAAC;IAEtE;IAGA,OAAOQ,YAAY,CAACW,IAAI,CAAC,IAAIE,IAAI,GAAGnB,MAAM,GAAGF,GAAG,CAAC;EACnD,CAAC,CAAC,CACH,CAACyB,IAAI,CAAC,EAAE,CACX,CAAC;AACH;AAAC", "ignoreList": []}