{"version": 3, "names": ["_t", "require", "traverseFast", "VISITOR_KEYS", "TokenMap", "constructor", "ast", "tokens", "source", "_tokens", "_source", "_nodesToTokenIndexes", "Map", "_nodesOccurrencesCountCache", "_tokensCache", "node", "indexes", "_getTokensIndexesOfNode", "length", "set", "has", "getIndexes", "get", "find", "condition", "k", "index", "tok", "findLastIndex", "findMatching", "test", "occurrenceCount", "i", "count", "cache", "matchesOriginal", "token", "end", "start", "value", "startsWith", "startMatches", "endMatches", "first", "last", "_findTokensOfNode", "low", "children", "childrenIterator", "type", "declaration", "next", "child", "childTok", "high", "push", "cached", "_findFirstTokenOfNode", "_findLastTokenOfNode", "mid", "exports", "quasis", "expressions", "keys", "key", "Array", "isArray"], "sources": ["../src/token-map.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\nimport type { Token } from \"@babel/parser\";\n\nimport { traverseFast, VISITOR_KEYS } from \"@babel/types\";\n\nexport class TokenMap {\n  _tokens: Token[];\n  _source: string;\n\n  _nodesToTokenIndexes: Map<t.Node, number[]> = new Map();\n  _nodesOccurrencesCountCache: Map<\n    t.Node,\n    { test: string; count: number; i: number }\n  > = new Map();\n\n  _tokensCache = new Map<t.Node, { first: number; last: number }>();\n\n  constructor(ast: t.Node, tokens: Token[], source: string) {\n    this._tokens = tokens;\n    this._source = source;\n\n    traverseFast(ast, node => {\n      const indexes = this._getTokensIndexesOfNode(node);\n      if (indexes.length > 0) this._nodesToTokenIndexes.set(node, indexes);\n    });\n\n    this._tokensCache = null;\n  }\n\n  has(node: t.Node): boolean {\n    return this._nodesToTokenIndexes.has(node);\n  }\n\n  getIndexes(node: t.Node): readonly number[] | undefined {\n    return this._nodesToTokenIndexes.get(node);\n  }\n\n  find(\n    node: t.Node,\n    condition: (token: Token, index: number) => boolean,\n  ): Token | null {\n    const indexes = this._nodesToTokenIndexes.get(node);\n    if (indexes) {\n      for (let k = 0; k < indexes.length; k++) {\n        const index = indexes[k];\n        const tok = this._tokens[index];\n        if (condition(tok, index)) return tok;\n      }\n    }\n    return null;\n  }\n\n  findLastIndex(\n    node: t.Node,\n    condition: (token: Token, index: number) => boolean,\n  ): number {\n    const indexes = this._nodesToTokenIndexes.get(node);\n    if (indexes) {\n      for (let k = indexes.length - 1; k >= 0; k--) {\n        const index = indexes[k];\n        const tok = this._tokens[index];\n        if (condition(tok, index)) return index;\n      }\n    }\n    return -1;\n  }\n\n  findMatching(\n    node: t.Node,\n    test: string,\n    occurrenceCount: number = 0,\n  ): Token | null {\n    const indexes = this._nodesToTokenIndexes.get(node);\n    if (indexes) {\n      let i = 0;\n      const count = occurrenceCount;\n\n      // To avoid O(n^2) search when printing lists (such as arrays), we\n      // cache the last index of a given token for a given occurrence count.\n      // If then we are asked to find the next occurrence of the same token,\n      // we start from the index of the previously found token.\n      // This cache only kicks in after 2 tokens of the same type, to avoid\n      // overhead in the simple case of having unique tokens per node.\n      if (count > 1) {\n        const cache = this._nodesOccurrencesCountCache.get(node);\n        if (cache && cache.test === test && cache.count < count) {\n          i = cache.i + 1;\n          occurrenceCount -= cache.count + 1;\n        }\n      }\n\n      for (; i < indexes.length; i++) {\n        const tok = this._tokens[indexes[i]];\n        if (this.matchesOriginal(tok, test)) {\n          if (occurrenceCount === 0) {\n            if (count > 0) {\n              this._nodesOccurrencesCountCache.set(node, { test, count, i });\n            }\n            return tok;\n          }\n          occurrenceCount--;\n        }\n      }\n    }\n    return null;\n  }\n\n  matchesOriginal(token: Token, test: string) {\n    if (token.end - token.start !== test.length) return false;\n    if (token.value != null) return token.value === test;\n    return this._source.startsWith(test, token.start);\n  }\n\n  startMatches(node: t.Node, test: string): boolean {\n    const indexes = this._nodesToTokenIndexes.get(node);\n    if (!indexes) return false;\n    const tok = this._tokens[indexes[0]];\n    if (tok.start !== node.start) return false;\n    return this.matchesOriginal(tok, test);\n  }\n\n  endMatches(node: t.Node, test: string): boolean {\n    const indexes = this._nodesToTokenIndexes.get(node);\n    if (!indexes) return false;\n    const tok = this._tokens[indexes[indexes.length - 1]];\n    if (tok.end !== node.end) return false;\n    return this.matchesOriginal(tok, test);\n  }\n\n  _getTokensIndexesOfNode(node: t.Node): number[] {\n    if (node.start == null || node.end == null) return [];\n\n    const { first, last } = this._findTokensOfNode(\n      node,\n      0,\n      this._tokens.length - 1,\n    );\n\n    let low = first;\n\n    const children = childrenIterator(node);\n\n    if (\n      (node.type === \"ExportNamedDeclaration\" ||\n        node.type === \"ExportDefaultDeclaration\") &&\n      node.declaration &&\n      node.declaration.type === \"ClassDeclaration\"\n    ) {\n      // Exported class declarations can be not properly nested inside\n      // the export declaration that contains them. For example, in\n      // `@dec export class Foo {}` the `export` is covered by the\n      // ClassDeclaration range. Skip the class declaration from the list\n      // of children to skip, so that when looking for `export` we also\n      // traverse its tokens.\n      children.next();\n    }\n\n    const indexes = [];\n\n    for (const child of children) {\n      if (child == null) continue;\n      if (child.start == null || child.end == null) continue;\n\n      const childTok = this._findTokensOfNode(child, low, last);\n\n      const high = childTok.first;\n      for (let k = low; k < high; k++) indexes.push(k);\n\n      low = childTok.last + 1;\n    }\n\n    for (let k = low; k <= last; k++) indexes.push(k);\n\n    return indexes;\n  }\n\n  _findTokensOfNode(node: t.Node, low: number, high: number) {\n    const cached = this._tokensCache.get(node);\n    if (cached) return cached;\n\n    const first = this._findFirstTokenOfNode(node.start, low, high);\n    const last = this._findLastTokenOfNode(node.end, first, high);\n\n    this._tokensCache.set(node, { first, last });\n    return { first, last };\n  }\n\n  _findFirstTokenOfNode(start: number, low: number, high: number): number {\n    while (low <= high) {\n      const mid = (high + low) >> 1;\n      if (start < this._tokens[mid].start) {\n        high = mid - 1;\n      } else if (start > this._tokens[mid].start) {\n        low = mid + 1;\n      } else {\n        return mid;\n      }\n    }\n    return low;\n  }\n\n  _findLastTokenOfNode(end: number, low: number, high: number): number {\n    while (low <= high) {\n      const mid = (high + low) >> 1;\n      if (end < this._tokens[mid].end) {\n        high = mid - 1;\n      } else if (end > this._tokens[mid].end) {\n        low = mid + 1;\n      } else {\n        return mid;\n      }\n    }\n    return high;\n  }\n}\n\nfunction* childrenIterator(node: t.Node) {\n  // We need special handling to iterate TemplateLiteral\n  // children in order, since the two lists are interleaved.\n  if (node.type === \"TemplateLiteral\") {\n    yield node.quasis[0];\n    for (let i = 1; i < node.quasis.length; i++) {\n      yield node.expressions[i - 1];\n      yield node.quasis[i];\n    }\n    return;\n  }\n\n  const keys = VISITOR_KEYS[node.type];\n  for (const key of keys) {\n    const child = (node as any)[key];\n    if (!child) continue;\n    if (Array.isArray(child)) {\n      yield* child;\n    } else {\n      yield child;\n    }\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,EAAA,GAAAC,OAAA;AAA0D;EAAjDC,YAAY;EAAEC;AAAY,IAAAH,EAAA;AAE5B,MAAMI,QAAQ,CAAC;EAYpBC,WAAWA,CAACC,GAAW,EAAEC,MAAe,EAAEC,MAAc,EAAE;IAAA,KAX1DC,OAAO;IAAA,KACPC,OAAO;IAAA,KAEPC,oBAAoB,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvDC,2BAA2B,GAGvB,IAAID,GAAG,CAAC,CAAC;IAAA,KAEbE,YAAY,GAAG,IAAIF,GAAG,CAA0C,CAAC;IAG/D,IAAI,CAACH,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,OAAO,GAAGF,MAAM;IAErBN,YAAY,CAACI,GAAG,EAAES,IAAI,IAAI;MACxB,MAAMC,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAACF,IAAI,CAAC;MAClD,IAAIC,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE,IAAI,CAACP,oBAAoB,CAACQ,GAAG,CAACJ,IAAI,EAAEC,OAAO,CAAC;IACtE,CAAC,CAAC;IAEF,IAAI,CAACF,YAAY,GAAG,IAAI;EAC1B;EAEAM,GAAGA,CAACL,IAAY,EAAW;IACzB,OAAO,IAAI,CAACJ,oBAAoB,CAACS,GAAG,CAACL,IAAI,CAAC;EAC5C;EAEAM,UAAUA,CAACN,IAAY,EAAiC;IACtD,OAAO,IAAI,CAACJ,oBAAoB,CAACW,GAAG,CAACP,IAAI,CAAC;EAC5C;EAEAQ,IAAIA,CACFR,IAAY,EACZS,SAAmD,EACrC;IACd,MAAMR,OAAO,GAAG,IAAI,CAACL,oBAAoB,CAACW,GAAG,CAACP,IAAI,CAAC;IACnD,IAAIC,OAAO,EAAE;MACX,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,OAAO,CAACE,MAAM,EAAEO,CAAC,EAAE,EAAE;QACvC,MAAMC,KAAK,GAAGV,OAAO,CAACS,CAAC,CAAC;QACxB,MAAME,GAAG,GAAG,IAAI,CAAClB,OAAO,CAACiB,KAAK,CAAC;QAC/B,IAAIF,SAAS,CAACG,GAAG,EAAED,KAAK,CAAC,EAAE,OAAOC,GAAG;MACvC;IACF;IACA,OAAO,IAAI;EACb;EAEAC,aAAaA,CACXb,IAAY,EACZS,SAAmD,EAC3C;IACR,MAAMR,OAAO,GAAG,IAAI,CAACL,oBAAoB,CAACW,GAAG,CAACP,IAAI,CAAC;IACnD,IAAIC,OAAO,EAAE;MACX,KAAK,IAAIS,CAAC,GAAGT,OAAO,CAACE,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5C,MAAMC,KAAK,GAAGV,OAAO,CAACS,CAAC,CAAC;QACxB,MAAME,GAAG,GAAG,IAAI,CAAClB,OAAO,CAACiB,KAAK,CAAC;QAC/B,IAAIF,SAAS,CAACG,GAAG,EAAED,KAAK,CAAC,EAAE,OAAOA,KAAK;MACzC;IACF;IACA,OAAO,CAAC,CAAC;EACX;EAEAG,YAAYA,CACVd,IAAY,EACZe,IAAY,EACZC,eAAuB,GAAG,CAAC,EACb;IACd,MAAMf,OAAO,GAAG,IAAI,CAACL,oBAAoB,CAACW,GAAG,CAACP,IAAI,CAAC;IACnD,IAAIC,OAAO,EAAE;MACX,IAAIgB,CAAC,GAAG,CAAC;MACT,MAAMC,KAAK,GAAGF,eAAe;MAQ7B,IAAIE,KAAK,GAAG,CAAC,EAAE;QACb,MAAMC,KAAK,GAAG,IAAI,CAACrB,2BAA2B,CAACS,GAAG,CAACP,IAAI,CAAC;QACxD,IAAImB,KAAK,IAAIA,KAAK,CAACJ,IAAI,KAAKA,IAAI,IAAII,KAAK,CAACD,KAAK,GAAGA,KAAK,EAAE;UACvDD,CAAC,GAAGE,KAAK,CAACF,CAAC,GAAG,CAAC;UACfD,eAAe,IAAIG,KAAK,CAACD,KAAK,GAAG,CAAC;QACpC;MACF;MAEA,OAAOD,CAAC,GAAGhB,OAAO,CAACE,MAAM,EAAEc,CAAC,EAAE,EAAE;QAC9B,MAAML,GAAG,GAAG,IAAI,CAAClB,OAAO,CAACO,OAAO,CAACgB,CAAC,CAAC,CAAC;QACpC,IAAI,IAAI,CAACG,eAAe,CAACR,GAAG,EAAEG,IAAI,CAAC,EAAE;UACnC,IAAIC,eAAe,KAAK,CAAC,EAAE;YACzB,IAAIE,KAAK,GAAG,CAAC,EAAE;cACb,IAAI,CAACpB,2BAA2B,CAACM,GAAG,CAACJ,IAAI,EAAE;gBAAEe,IAAI;gBAAEG,KAAK;gBAAED;cAAE,CAAC,CAAC;YAChE;YACA,OAAOL,GAAG;UACZ;UACAI,eAAe,EAAE;QACnB;MACF;IACF;IACA,OAAO,IAAI;EACb;EAEAI,eAAeA,CAACC,KAAY,EAAEN,IAAY,EAAE;IAC1C,IAAIM,KAAK,CAACC,GAAG,GAAGD,KAAK,CAACE,KAAK,KAAKR,IAAI,CAACZ,MAAM,EAAE,OAAO,KAAK;IACzD,IAAIkB,KAAK,CAACG,KAAK,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACG,KAAK,KAAKT,IAAI;IACpD,OAAO,IAAI,CAACpB,OAAO,CAAC8B,UAAU,CAACV,IAAI,EAAEM,KAAK,CAACE,KAAK,CAAC;EACnD;EAEAG,YAAYA,CAAC1B,IAAY,EAAEe,IAAY,EAAW;IAChD,MAAMd,OAAO,GAAG,IAAI,CAACL,oBAAoB,CAACW,GAAG,CAACP,IAAI,CAAC;IACnD,IAAI,CAACC,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMW,GAAG,GAAG,IAAI,CAAClB,OAAO,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC;IACpC,IAAIW,GAAG,CAACW,KAAK,KAAKvB,IAAI,CAACuB,KAAK,EAAE,OAAO,KAAK;IAC1C,OAAO,IAAI,CAACH,eAAe,CAACR,GAAG,EAAEG,IAAI,CAAC;EACxC;EAEAY,UAAUA,CAAC3B,IAAY,EAAEe,IAAY,EAAW;IAC9C,MAAMd,OAAO,GAAG,IAAI,CAACL,oBAAoB,CAACW,GAAG,CAACP,IAAI,CAAC;IACnD,IAAI,CAACC,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMW,GAAG,GAAG,IAAI,CAAClB,OAAO,CAACO,OAAO,CAACA,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,IAAIS,GAAG,CAACU,GAAG,KAAKtB,IAAI,CAACsB,GAAG,EAAE,OAAO,KAAK;IACtC,OAAO,IAAI,CAACF,eAAe,CAACR,GAAG,EAAEG,IAAI,CAAC;EACxC;EAEAb,uBAAuBA,CAACF,IAAY,EAAY;IAC9C,IAAIA,IAAI,CAACuB,KAAK,IAAI,IAAI,IAAIvB,IAAI,CAACsB,GAAG,IAAI,IAAI,EAAE,OAAO,EAAE;IAErD,MAAM;MAAEM,KAAK;MAAEC;IAAK,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAC5C9B,IAAI,EACJ,CAAC,EACD,IAAI,CAACN,OAAO,CAACS,MAAM,GAAG,CACxB,CAAC;IAED,IAAI4B,GAAG,GAAGH,KAAK;IAEf,MAAMI,QAAQ,GAAGC,gBAAgB,CAACjC,IAAI,CAAC;IAEvC,IACE,CAACA,IAAI,CAACkC,IAAI,KAAK,wBAAwB,IACrClC,IAAI,CAACkC,IAAI,KAAK,0BAA0B,KAC1ClC,IAAI,CAACmC,WAAW,IAChBnC,IAAI,CAACmC,WAAW,CAACD,IAAI,KAAK,kBAAkB,EAC5C;MAOAF,QAAQ,CAACI,IAAI,CAAC,CAAC;IACjB;IAEA,MAAMnC,OAAO,GAAG,EAAE;IAElB,KAAK,MAAMoC,KAAK,IAAIL,QAAQ,EAAE;MAC5B,IAAIK,KAAK,IAAI,IAAI,EAAE;MACnB,IAAIA,KAAK,CAACd,KAAK,IAAI,IAAI,IAAIc,KAAK,CAACf,GAAG,IAAI,IAAI,EAAE;MAE9C,MAAMgB,QAAQ,GAAG,IAAI,CAACR,iBAAiB,CAACO,KAAK,EAAEN,GAAG,EAAEF,IAAI,CAAC;MAEzD,MAAMU,IAAI,GAAGD,QAAQ,CAACV,KAAK;MAC3B,KAAK,IAAIlB,CAAC,GAAGqB,GAAG,EAAErB,CAAC,GAAG6B,IAAI,EAAE7B,CAAC,EAAE,EAAET,OAAO,CAACuC,IAAI,CAAC9B,CAAC,CAAC;MAEhDqB,GAAG,GAAGO,QAAQ,CAACT,IAAI,GAAG,CAAC;IACzB;IAEA,KAAK,IAAInB,CAAC,GAAGqB,GAAG,EAAErB,CAAC,IAAImB,IAAI,EAAEnB,CAAC,EAAE,EAAET,OAAO,CAACuC,IAAI,CAAC9B,CAAC,CAAC;IAEjD,OAAOT,OAAO;EAChB;EAEA6B,iBAAiBA,CAAC9B,IAAY,EAAE+B,GAAW,EAAEQ,IAAY,EAAE;IACzD,MAAME,MAAM,GAAG,IAAI,CAAC1C,YAAY,CAACQ,GAAG,CAACP,IAAI,CAAC;IAC1C,IAAIyC,MAAM,EAAE,OAAOA,MAAM;IAEzB,MAAMb,KAAK,GAAG,IAAI,CAACc,qBAAqB,CAAC1C,IAAI,CAACuB,KAAK,EAAEQ,GAAG,EAAEQ,IAAI,CAAC;IAC/D,MAAMV,IAAI,GAAG,IAAI,CAACc,oBAAoB,CAAC3C,IAAI,CAACsB,GAAG,EAAEM,KAAK,EAAEW,IAAI,CAAC;IAE7D,IAAI,CAACxC,YAAY,CAACK,GAAG,CAACJ,IAAI,EAAE;MAAE4B,KAAK;MAAEC;IAAK,CAAC,CAAC;IAC5C,OAAO;MAAED,KAAK;MAAEC;IAAK,CAAC;EACxB;EAEAa,qBAAqBA,CAACnB,KAAa,EAAEQ,GAAW,EAAEQ,IAAY,EAAU;IACtE,OAAOR,GAAG,IAAIQ,IAAI,EAAE;MAClB,MAAMK,GAAG,GAAIL,IAAI,GAAGR,GAAG,IAAK,CAAC;MAC7B,IAAIR,KAAK,GAAG,IAAI,CAAC7B,OAAO,CAACkD,GAAG,CAAC,CAACrB,KAAK,EAAE;QACnCgB,IAAI,GAAGK,GAAG,GAAG,CAAC;MAChB,CAAC,MAAM,IAAIrB,KAAK,GAAG,IAAI,CAAC7B,OAAO,CAACkD,GAAG,CAAC,CAACrB,KAAK,EAAE;QAC1CQ,GAAG,GAAGa,GAAG,GAAG,CAAC;MACf,CAAC,MAAM;QACL,OAAOA,GAAG;MACZ;IACF;IACA,OAAOb,GAAG;EACZ;EAEAY,oBAAoBA,CAACrB,GAAW,EAAES,GAAW,EAAEQ,IAAY,EAAU;IACnE,OAAOR,GAAG,IAAIQ,IAAI,EAAE;MAClB,MAAMK,GAAG,GAAIL,IAAI,GAAGR,GAAG,IAAK,CAAC;MAC7B,IAAIT,GAAG,GAAG,IAAI,CAAC5B,OAAO,CAACkD,GAAG,CAAC,CAACtB,GAAG,EAAE;QAC/BiB,IAAI,GAAGK,GAAG,GAAG,CAAC;MAChB,CAAC,MAAM,IAAItB,GAAG,GAAG,IAAI,CAAC5B,OAAO,CAACkD,GAAG,CAAC,CAACtB,GAAG,EAAE;QACtCS,GAAG,GAAGa,GAAG,GAAG,CAAC;MACf,CAAC,MAAM;QACL,OAAOA,GAAG;MACZ;IACF;IACA,OAAOL,IAAI;EACb;AACF;AAACM,OAAA,CAAAxD,QAAA,GAAAA,QAAA;AAED,UAAU4C,gBAAgBA,CAACjC,IAAY,EAAE;EAGvC,IAAIA,IAAI,CAACkC,IAAI,KAAK,iBAAiB,EAAE;IACnC,MAAMlC,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAAC;IACpB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,IAAI,CAAC8C,MAAM,CAAC3C,MAAM,EAAEc,CAAC,EAAE,EAAE;MAC3C,MAAMjB,IAAI,CAAC+C,WAAW,CAAC9B,CAAC,GAAG,CAAC,CAAC;MAC7B,MAAMjB,IAAI,CAAC8C,MAAM,CAAC7B,CAAC,CAAC;IACtB;IACA;EACF;EAEA,MAAM+B,IAAI,GAAG5D,YAAY,CAACY,IAAI,CAACkC,IAAI,CAAC;EACpC,KAAK,MAAMe,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMX,KAAK,GAAIrC,IAAI,CAASiD,GAAG,CAAC;IAChC,IAAI,CAACZ,KAAK,EAAE;IACZ,IAAIa,KAAK,CAACC,OAAO,CAACd,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK;IACd,CAAC,MAAM;MACL,MAAMA,KAAK;IACb;EACF;AACF", "ignoreList": []}