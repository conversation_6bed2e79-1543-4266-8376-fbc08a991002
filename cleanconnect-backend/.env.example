# Server Configuration
PORT=3000
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# JWT Configuration (if using custom JWT)
JWT_SECRET=your_jwt_secret_key

# Database Configuration (if using direct connection)
DATABASE_URL=your_database_connection_string

# External Services (optional)
FCM_SERVER_KEY=your_fcm_server_key
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token




# # Server Configuration
# PORT=3000
# NODE_ENV=development

# # Supabase Configuration
# SUPABASE_URL=https://ximpniwvkkwuuencurtj.supabase.co
# SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Y2ATFYdaTAT9uVNj-ZBa7zaoFjML7SpR-uS5jdAGGQE
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************._c-ZyFYPf57yJcBCMKPhxjPiWKhT1dp7JF5qdojICYc

# # CORS Configuration
# ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# # JWT Configuration (if using custom JWT)
# JWT_SECRET="0K+Po9BpcxoWpX8uoWOmy7GEPhKz0CF2j2nb/SmzcjTPiFs4gxchyGmcgdZIM3fniZgP1HLaSJ6tnxM5dPF6wg=="

# # Database Configuration (if using direct connection)
# DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# # External Services (optional)
# FCM_SERVER_KEY=your_fcm_server_key
# TWILIO_ACCOUNT_SID=your_twilio_account_sid
# TWILIO_AUTH_TOKEN=your_twilio_auth_token
