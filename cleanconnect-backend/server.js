// Load environment variables first
require("dotenv").config()

const express = require("express")
const cors = require("cors")
const helmet = require("helmet")
const rateLimit = require("express-rate-limit")
const { supabase } = require("./config/supabase")

const app = express()
const PORT = process.env.PORT || 3000
const NODE_ENV = process.env.NODE_ENV || 'development'
const API_URL = process.env.API_URL || `http://localhost:${PORT}`

// Middleware
app.use(helmet())
app.use(
  cors({
    origin: process.env.ALLOWED_ORIGINS?.split(",") || ["http://localhost:3000"],
    credentials: true,
  }),
)

app.use(express.json({ limit: "10mb" }))
app.use(express.urlencoded({ extended: true }))

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP, please try again later.",
})
app.use(limiter)

// Import routes
const authRoutes = require("./routes/auth")
const bookingRoutes = require("./routes/bookings")
const providerRoutes = require("./routes/providers")
const adminRoutes = require("./routes/admin")
const userRoutes = require("./routes/users")
const serviceRoutes = require("./routes/services")
const reviewRoutes = require("./routes/reviews")
const notificationRoutes = require("./routes/notifications")

// JWT Authentication middleware
const { authenticateToken } = require("./middleware/auth")

// Routes
app.use("/api/auth", authRoutes)
app.use("/api/bookings", authenticateToken, bookingRoutes)
app.use("/api/providers", authenticateToken, providerRoutes)
app.use("/api/admin", authenticateToken, adminRoutes)
app.use("/api/users", authenticateToken, userRoutes)
app.use("/api/services", serviceRoutes)
app.use("/api/reviews", authenticateToken, reviewRoutes)
app.use("/api/notifications", authenticateToken, notificationRoutes)

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    environment: NODE_ENV,
    port: PORT,
    api_url: API_URL,
    version: "1.0.0"
  })
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({
    error: "Something went wrong!",
    message: process.env.NODE_ENV === "development" ? err.message : undefined,
  })
})

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({ error: "Route not found" })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 CleanConnect Backend running on port ${PORT}`)
  console.log(`📍 API URL: ${API_URL}`)
  console.log(`🌍 Environment: ${NODE_ENV}`)
  console.log(`🔗 Health check: ${API_URL}/health`)
  console.log(`📚 API Base: ${API_URL}/api`)
})

module.exports = { app, supabase }
