# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Logs
logs
*.log

# Database
*.sqlite
*.sqlite3
*.db

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old

# Test files
test-results/
coverage/

# Build artifacts
build/
dist/
*.tgz
*.tar.gz

# React Native
# React Native CLI
.react-native/

# Metro
.metro-health-check*

# Flipper
.flipper/

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/
/ios/Podfile.lock

# Expo
.expo/
.expo-shared/
expo-env.d.ts

# Android
/android/app/build/
/android/app/release/
/android/build/
/android/.gradle/
/android/local.properties
/android/app/src/main/assets/index.android.bundle
/android/app/src/main/res/drawable-*/
/android/keystores/
*.keystore
*.jks

# iOS
/ios/build/
/ios/DerivedData/
/ios/*.xcworkspace/xcuserdata/
/ios/*.xcodeproj/xcuserdata/
/ios/*.xcodeproj/project.xcworkspace/xcuserdata/
/ios/Pods/
*.ipa
*.app.dSYM.zip
*.app.dSYM

# Fastlane
/ios/fastlane/report.xml
/ios/fastlane/Preview.html
/ios/fastlane/screenshots
/ios/fastlane/test_output
/android/fastlane/report.xml
/android/fastlane/Preview.html
/android/fastlane/screenshots
/android/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
/vendor/bundle/
Gemfile.lock

# Watchman
.watchmanconfig

# Buck
buck-out/
\.buckd/
*.keystore
!debug.keystore

# Firebase
google-services.json
GoogleService-Info.plist
firebase-debug.log
firestore-debug.log

# Sentry
.sentryclirc

# ESLint
.eslintrc.js

# Prettier
.prettierrc.js

# EditorConfig
.editorconfig

# Security and Sensitive Files
*.pem
*.key
*.crt
*.p12
*.pfx
secrets/
credentials/
config/production.json
config/staging.json

# API Keys and Tokens
.api-keys
.tokens
.secrets

# Database dumps and backups
.sql
*.dump
*.backup

# Documentation builds
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# IPython
profile_default/
ipython_config.py

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Project specific
uploads/
public/uploads/
storage/
.storage/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Local development
.local/
local/
dev/
development/

# Monitoring and Analytics
.newrelic.yml
newrelic.js